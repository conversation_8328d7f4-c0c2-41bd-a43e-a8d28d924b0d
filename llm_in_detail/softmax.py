import torch
from torch import nn
from loguru import logger
import math
import numpy as np

import torch.nn.functional as F
import pytest


def softmax(x):
    return torch.exp(x) / torch.sum(torch.exp(x), axis=-1, keepdim=True)


def stable_softmax(x, dim=-1):
    max_val = torch.max(x, dim=dim, keepdim=True).values
    exp_x = torch.exp(x - max_val)

    logger.debug(f"row exp sum {torch.sum(exp_x, axis=dim, keepdim=True)}")
    return exp_x / torch.sum(exp_x, axis=dim, keepdim=True)


@pytest.mark.parametrize(
    "bs, seq_len, hidden_size",
    [
        (2, 3, 8),
        (3, 4, 128),
        (4, 5, 128),
        (6, 6, 128),
    ],
)
def test_softmax(bs, seq_len, hidden_size):
    x = torch.randn(bs, seq_len, hidden_size)

    a = softmax(x)

    golden = torch.softmax(x, dim=-1)

    logger.debug(f"{x=}")
    logger.debug(f"golden {golden}")

    assert torch.allclose(softmax(x), torch.softmax(x, dim=-1))
    assert torch.allclose(stable_softmax(x), torch.softmax(x, dim=-1))


def online_softmax(x, block_size=4):
    bs, seq_len, hidden_size = x.shape

    result = torch.zeros_like(x)
    acc = torch.zeros((bs, seq_len, 1))
    current_max = torch.zeros((bs, seq_len, 1))
    for i in range(0, hidden_size, block_size):
        new_max = torch.max(x[:, :, i : i + block_size], dim=-1, keepdim=True).values
        new_max = torch.max(new_max, current_max)
        # print(new_max)
        acc = torch.sum(
            torch.exp(x[:, :, i : i + block_size] - new_max), axis=-1, keepdim=True
        ) + acc * torch.exp(current_max - new_max)
        current_max = new_max

    for i in range(0, hidden_size, block_size):
        result[:, :, i : i + block_size] = (
            torch.exp(x[:, :, i : i + block_size] - current_max) / acc
        )
    return result


@pytest.mark.parametrize(
    "bs, seq_len, hidden_size",
    [
        (2, 3, 8),
        (3, 4, 128),
        (4, 5, 128),
        (6, 6, 128),
    ],
)
def test_online_softmax(bs, seq_len, hidden_size):
    x = torch.randn(bs, seq_len, hidden_size)
    a = online_softmax(x)

    assert torch.allclose(online_softmax(x), torch.softmax(x, dim=-1))
