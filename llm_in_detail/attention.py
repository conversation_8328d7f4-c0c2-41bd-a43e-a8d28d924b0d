import torch
from torch import nn
from loguru import logger
import math
import numpy as np

import torch.nn.functional as F

import pytest


def simple_attention(q, k, v):
    scale = 1 / np.sqrt(q.shape[-1])
    q = q * scale

    scores = torch.matmul(q, k.transpose(-2, -1))

    logger.debug(f"scores: {scores.shape}")

    scores = torch.softmax(scores, dim=-1)
    output = torch.matmul(scores, v)

    logger.debug(f"scores: {scores.shape}")

    output = torch.matmul(scores, v)

    return output


# for mha, single_batch
def flash_attn(
    q, k, v, dropout_p=0.0, softmax_scale=None, debug=False, tile_row=2, tile_col=3
):
    qlen, klen = q.shape[-2], k.shape[-2]
    hidden_size = q.shape[-1]
    scale = 1 / np.sqrt(q.shape[-1])
    q = q * scale
    o = torch.zeros_like(q)
    l = torch.zeros(qlen, dtype=torch.float32)
    m = torch.ones(qlen, dtype=torch.float32) * -torch.inf

    for i in range(0, klen, tile_col):
        k_tile = k[i : i + tile_col, :]
        v_tile = v[i : i + tile_col, :]
        for j in range(0, qlen, tile_row):
            q_tile = q[j : j + tile_row, :]
            o_tile = q[j : j + tile_row, :]
            s_tile = torch.matmul(q_tile, k_tile.transpose(-2, -1))
            m_tile = torch.max(s_tile, dim=-1, keepdim=False).values

            # logger.debug(f"s_tile: {s_tile}")
            # logger.debug(f"m_tile: {m_tile}")
            p_tile = torch.exp(s_tile - m_tile.unsqueeze(-1))

            l_tile = torch.sum(p_tile, dim=-1, keepdim=False)

            # m_tile = m_tile.squeeze(-1)
            m_new = torch.maximum(m_tile, m[j : j + tile_row])

            l_new = (
                torch.exp(m[j : j + tile_row] - m_new) * l[j : j + tile_row]
                + torch.exp(m_tile - m_new) * l_tile
            )
            print('l_new', l_new)

            t1 = torch.diag(1/ l_new) @ torch.diag(l[j : j + tile_row]) @ torch.exp(m[j : j + tile_row] - m_new) @ o_tile

            t21 = torch.diag(1/ l_new) @ torch.diag(torch.exp(m_tile - m_new))
            t22 = p_tile @ v_tile
            t2 = t21 @ (p_tile @ v_tile)

            # t1 = (
            #     l[j : j + tile_row]
            #     / l_new.squeeze(-1)
            #     * torch.exp(m[j : j + tile_row] - m_new)
            # ).unsqueeze(-1) * o_tile

            # t1 = (
            #     l[j : j + tile_row]
            #     / l_new.squeeze(-1)
            #     * torch.exp(m[j : j + tile_row] - m_new)
            # ).unsqueeze(-1) * o_tile
            # t11 = (
            #     l[j : j + tile_row]
            #     / l_new.squeeze(-1)
            #     * torch.exp(m[j : j + tile_row] - m_new)
            # ).unsqueeze(-1)

            # t21 = torch.exp(m_tile - m_new) / l_new
            # t22 = p_tile @ v_tile
            # t2 = (torch.exp(m_tile - m_new) / l_new).unsqueeze(-1) * (p_tile @ v_tile)



            if debug:
                logger.debug(f"p_tile: {p_tile}")
                logger.debug(f"l_tile: {l_tile}")
                logger.debug(f"m_new: {m_new}")
                logger.debug(f"l_new: {l_new}")
                logger.debug(f"o_tile: {o_tile}")
                # logger.debug(f"t11: {t11}")
                logger.debug(f"t1: {t1}")
                print(f"{t1.shape=}")
                print(f"{t21.shape=}")
                print(f"{t22.shape=}")
                print(f"{t2.shape=}")

            o[j : j + tile_row, :] = t1 + t2
            l[j : j + tile_row] = l_new.squeeze(-1)
            m[j : j + tile_row] = m_new

        #     break
        # break
    # print(f'{o=}')
    if debug:
        print(f"{l=}")
        print(f"{m=}")
    return o


@pytest.mark.parametrize(
    "q_len, seq_len, hidden_size, tile_row, tile_col",
    [
        (1, 3, 8, 2, 3),
        (3, 4, 128, 3, 2),
        (8, 8, 128, 2, 4),
        (1, 128, 128, 2, 4),
    ],
)
def test_flash_attn(q_len, seq_len, hidden_size, tile_row, tile_col):
    torch.manual_seed(0)

    q = torch.randn(q_len, hidden_size)
    k = torch.randn(seq_len, hidden_size)
    v = torch.randn(seq_len, hidden_size)

    o = flash_attn(q, k, v, tile_row=tile_row, tile_col=tile_col, debug=True)

    o2 = simple_attention(q, k, v)

    print(f"{o.shape=}")
    print(f"{o2.shape=}")

    print(f"{o=}")
    print(f"{o2=}")

    assert torch.allclose(o, o2)


if __name__ == "__main__":
    print("---------0-------")
    test_flash_attn(2, 2, 2, 2, 2)
    print("---------1-------")
    test_flash_attn(2, 4, 2, 2, 2)
    print("---------1-------")
    test_flash_attn(3, 4, 2, 3, 2)
    print("---------2-------")
    test_flash_attn(3, 4, 2, 3, 2)
